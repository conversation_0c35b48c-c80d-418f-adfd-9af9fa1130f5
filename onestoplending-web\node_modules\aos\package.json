{"name": "aos", "version": "2.3.4", "description": "Animate on scroll library", "homepage": "https://michalsnik.github.io/aos/", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "main": "dist/aos.js", "repository": {"type": "git", "url": "https://github.com/michalsnik/aos.git"}, "bugs": {"url": "https://github.com/michalsnik/aos/issues"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.9.1", "babel-loader": "^6.2.4", "babel-plugin-transform-object-assign": "^6.8.0", "babel-polyfill": "^6.9.1", "babel-preset-es2015": "^6.9.0", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^1.0.1", "jasmine-core": "^2.3.4", "jasmine-fixture": "^2.0.0", "jasmine-jquery": "^2.1.1", "jquery": "^3.1.1", "karma": "^1.4.0", "karma-chrome-launcher": "^2.0.0", "karma-jasmine": "^1.1.0", "karma-jasmine-jquery": "^0.1.1", "karma-webpack": "^2.0.1", "node-sass": "^4.3.0", "phantomjs": "^2.1.7", "postcss-loader": "^2.0.6", "sass-loader": "^4.1.1", "style-loader": "^0.18.2", "webpack": "^1.13.1", "webpack-dev-server": "^1.14.1"}, "dependencies": {"classlist-polyfill": "^1.0.3", "lodash.debounce": "^4.0.6", "lodash.throttle": "^4.0.1"}, "scripts": {"test": "node ./node_modules/karma/bin/karma start karma.conf.js", "build": "webpack", "dev": "node ./node_modules/webpack-dev-server/bin/webpack-dev-server.js"}}
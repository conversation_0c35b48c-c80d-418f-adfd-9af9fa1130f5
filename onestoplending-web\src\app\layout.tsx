import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import <PERSON>rip<PERSON> from "next/script";
import "./globals.css";
import StructuredData from '@/components/StructuredData';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://onestoplendingcanada.ca'),
  title: "One Stop Lending Canada - Healthcare Financing Solutions | Medical Payment Plans",
  description: "Professional healthcare financing solutions in Canada. Fast approvals for medical procedures, dental work, cosmetic surgery, and more. Flexible payment plans with competitive rates.",
  keywords: "healthcare financing Canada, medical financing, dental financing, cosmetic surgery financing, healthcare payment plans, medical loans Canada, patient financing, healthcare credit, medical procedure financing, dental payment plans",
  authors: [{ name: "One Stop Lending Canada" }],
  creator: "One Stop Lending Canada",
  publisher: "One Stop Lending Canada",
  robots: "index, follow",
  viewport: "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",
  openGraph: {
    type: "website",
    locale: "en_CA",
    url: "https://onestoplendingcanada.ca",
    title: "One Stop Lending Canada - Healthcare Financing Solutions",
    description: "Professional healthcare financing solutions in Canada. Fast approvals for medical procedures, dental work, cosmetic surgery, and more.",
    siteName: "One Stop Lending Canada",
    images: [
      {
        url: "/logo.png",
        width: 1200,
        height: 630,
        alt: "One Stop Lending Canada - Healthcare Financing",
      },
    ],
  },

  twitter: {
    card: "summary_large_image",
    title: "One Stop Lending Canada - Healthcare Financing Solutions",
    description: "Professional healthcare financing solutions in Canada. Fast approvals for medical procedures and dental work.",
    images: ["/logo.png"],
  },

  alternates: {
    canonical: "https://onestoplendingcanada.ca",
  },

  other: {
    "geo.region": "CA",
    "geo.placename": "Canada",
    "geo.position": "56.1304;-106.3468",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <StructuredData />
        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'GA_MEASUREMENT_ID');
          `}
        </Script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}

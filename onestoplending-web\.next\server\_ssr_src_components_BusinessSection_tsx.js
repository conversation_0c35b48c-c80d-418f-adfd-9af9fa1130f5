"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_BusinessSection_tsx";
exports.ids = ["_ssr_src_components_BusinessSection_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/BusinessSection.tsx":
/*!********************************************!*\
  !*** ./src/components/BusinessSection.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BusinessSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst businessFeatures = [\n    {\n        icon: '🚀',\n        title: 'Increase Profits',\n        description: 'Our point-of-sale financing solutions allow your customers to make big-ticket purchases today while paying over time. This increases your sales volume and overall profit.'\n    },\n    {\n        icon: '⚡',\n        title: 'Lightning-Fast Approvals',\n        description: 'Get customer approvals within seconds with our quick application process and get your funds direct deposited within a few days of the deal being booked.'\n    },\n    {\n        icon: '💻',\n        title: '100% Online Process',\n        description: 'Complete applications in minutes and get instant credit decisions right at the point of sale. Use any device to submit applications and sign agreements electronically.'\n    },\n    {\n        icon: '🤝',\n        title: 'We\\'re Here for You',\n        description: 'We\\'re here to support you. You\\'ll speak to real people with a wealth of industry knowledge. Contact us in the way that works best for you.'\n    }\n];\nconst stats = [\n    {\n        number: '6K+',\n        label: 'Partners'\n    },\n    {\n        number: '11+',\n        label: 'Years as Canada\\'s leading powersports lender'\n    },\n    {\n        number: '$10B+',\n        label: 'In loan applications processed'\n    }\n];\nfunction BusinessSection() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 50\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"businesses\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    ref: ref,\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Point-of-Sale Financing\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gradient\",\n                                    children: \"Designed for Businesses\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-onestop-gray-dark max-w-4xl mx-auto leading-relaxed\",\n                            children: \"In point-of-sale financing, we know you have choices. But with our all-in-one financing center offering instant online credit decisions, real-time customer verification, and industry-leading dealer reserves, we believe we're the obvious choice.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: isInView ? \"visible\" : \"hidden\",\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-20\",\n                    children: businessFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            variants: itemVariants,\n                            className: \"bg-gray-50 p-8 rounded-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: feature.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-onestop-gray-dark leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-12\",\n                            children: \"The Numbers Speak for Themselves\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: isInView ? {\n                                        opacity: 1,\n                                        scale: 1\n                                    } : {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.6 + index * 0.1\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl md:text-5xl font-bold text-onestop-blue mb-2\",\n                                            children: stat.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-onestop-gray-dark text-lg\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.8\n                    },\n                    className: \"text-center bg-gray-50 p-12 rounded-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-6\",\n                            children: \"Partner with us and start submitting loan applications within days\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"#contact\",\n                            className: \"btn-professional text-white px-8 py-4 rounded-lg text-lg font-semibold inline-block\",\n                            children: \"Join Our Network\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/BusinessSection.tsx\n");

/***/ })

};
;
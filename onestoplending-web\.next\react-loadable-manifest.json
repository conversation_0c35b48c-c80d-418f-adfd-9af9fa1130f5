{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app", "files": ["static/chunks/_pages-dir-browser_node_modules_next_dist_pages__app_js.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error", "files": ["static/chunks/_pages-dir-browser_node_modules_next_dist_pages__error_js.js"]}, "app\\page.tsx -> @/components/Contact": {"id": "app\\page.tsx -> @/components/Contact", "files": ["static/chunks/_app-pages-browser_src_components_Contact_tsx.js"]}, "app\\page.tsx -> @/components/ExpertAssistance": {"id": "app\\page.tsx -> @/components/ExpertAssistance", "files": ["static/chunks/_app-pages-browser_src_components_ExpertAssistance_tsx.js"]}, "app\\page.tsx -> @/components/Features": {"id": "app\\page.tsx -> @/components/Features", "files": ["static/chunks/_app-pages-browser_src_components_Features_tsx.js"]}, "app\\page.tsx -> @/components/Footer": {"id": "app\\page.tsx -> @/components/Footer", "files": ["static/chunks/_app-pages-browser_src_components_Footer_tsx.js"]}, "app\\page.tsx -> @/components/StreamlineFinancing": {"id": "app\\page.tsx -> @/components/StreamlineFinancing", "files": ["static/chunks/_app-pages-browser_src_components_StreamlineFinancing_tsx.js"]}}
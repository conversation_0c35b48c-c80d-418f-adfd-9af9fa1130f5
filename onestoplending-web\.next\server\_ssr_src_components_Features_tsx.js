"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_Features_tsx";
exports.ids = ["_ssr_src_components_Features_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/Features.tsx":
/*!*************************************!*\
  !*** ./src/components/Features.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst features = [\n    {\n        icon: '⚡',\n        title: 'Fast Approvals',\n        description: 'Get approved in minutes with our streamlined application process and instant credit decisions.'\n    },\n    {\n        icon: '🤝',\n        title: 'Committed Service',\n        description: 'Our dedicated team provides exceptional service and support throughout your financing journey.'\n    },\n    {\n        icon: '💻',\n        title: '100% Online Process',\n        description: 'Complete applications, upload documents, and sign agreements electronically from any device.'\n    },\n    {\n        icon: '📈',\n        title: 'Increase Your Sales',\n        description: 'Our point-of-sale financing solutions help your customers make purchases while paying over time.'\n    },\n    {\n        icon: '🔒',\n        title: 'Secure & Reliable',\n        description: 'Bank-level security with transparent pricing and no hidden fees or surprises.'\n    },\n    {\n        icon: '📞',\n        title: 'Expert Support',\n        description: 'Speak to real people with industry knowledge who respond quickly to your needs.'\n    }\n];\nfunction Features() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 50\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    ref: ref,\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4 font-montserrat\",\n                            children: \"Fast Approvals,\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl md:text-4xl font-bold text-onestop-blue mb-6 font-montserrat\",\n                            children: \"Committed Service\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-4xl mx-auto font-montserrat leading-relaxed\",\n                            children: \"We understand that you have options when it comes to financing solutions at One Stop Lending Canada. Our fast approvals and our exceptional service are what set us apart. With our online financing portal, the entire financing process is streamlined offering real-time customer verification, instant credit decisions, and quick funding.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: isInView ? \"visible\" : \"hidden\",\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: itemVariants,\n                            whileHover: {\n                                scale: 1.05,\n                                transition: {\n                                    duration: 0.2\n                                }\n                            },\n                            className: \"bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    className: \"text-4xl mb-6 text-onestop-blue\",\n                                    children: feature.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4 font-montserrat\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed font-montserrat\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"text-center mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-8 rounded-lg shadow-md border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700 font-montserrat leading-relaxed max-w-4xl mx-auto\",\n                            children: \"Friendly Easy Consumer Financing Solutions To Partner With Your Business. Auto Consumer Financing, Healthcare Consumer Financing, Motorcycles, Jet Skis, ATVs, RVs, Retail Purchases. All of these services are market-leading, providing tailored service offerings to the various sectors that our clients and their customers come from. Choose One Stop Lending Canada for a financing experience that is committed to your future.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Features.tsx\n");

/***/ })

};
;
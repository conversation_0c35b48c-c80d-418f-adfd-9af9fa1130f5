"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_Features_tsx";
exports.ids = ["_ssr_src_components_Features_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/Features.tsx":
/*!*************************************!*\
  !*** ./src/components/Features.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst features = [\n    {\n        icon: '🏥',\n        title: 'Healthcare Financing',\n        description: 'Specialized financing for medical procedures, dental work, cosmetic surgery, and healthcare services.'\n    },\n    {\n        icon: '⚡',\n        title: 'Fast Approvals',\n        description: 'Get approved in minutes with our streamlined application process and instant credit decisions.'\n    },\n    {\n        icon: '💻',\n        title: '100% Online Process',\n        description: 'Complete applications, upload documents, and sign agreements electronically from any device.'\n    },\n    {\n        icon: '🚗',\n        title: 'Auto Financing',\n        description: 'Vehicle financing solutions with competitive rates for cars, motorcycles, and recreational vehicles.'\n    },\n    {\n        icon: '🔒',\n        title: 'Secure & Reliable',\n        description: 'Bank-level security with transparent pricing and no hidden fees or surprises.'\n    },\n    {\n        icon: '📞',\n        title: 'Expert Support',\n        description: 'Speak to real people with industry knowledge who respond quickly to your needs.'\n    }\n];\nfunction Features() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 50\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    ref: ref,\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4 font-montserrat\",\n                            children: \"Fast Approvals,\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl md:text-4xl font-bold text-onestop-blue mb-6 font-montserrat\",\n                            children: \"Committed Service\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-4xl mx-auto font-montserrat leading-relaxed\",\n                            children: [\n                                \"We understand that you have options when it comes to \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"healthcare financing solutions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 66\n                                }, this),\n                                \" in Canada. Our fast approvals and exceptional service are what set us apart. With our online financing portal, the entire financing process is streamlined offering real-time customer verification, instant credit decisions, and quick funding for medical procedures.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: isInView ? \"visible\" : \"hidden\",\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: itemVariants,\n                            whileHover: {\n                                scale: 1.05,\n                                transition: {\n                                    duration: 0.2\n                                }\n                            },\n                            className: \"bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    className: \"text-4xl mb-6 text-onestop-blue\",\n                                    children: feature.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4 font-montserrat\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed font-montserrat\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                                        alt: \"Healthcare financing solutions\",\n                                        className: \"w-full h-80 object-cover rounded-lg shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-t from-onestop-blue/20 to-transparent rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-8 rounded-lg shadow-md border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-onestop-blue mb-4 font-montserrat\",\n                                        children: \"Healthcare Financing Solutions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-700 font-montserrat leading-relaxed\",\n                                        children: [\n                                            \"Professional financing solutions for healthcare providers and patients. \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Healthcare Consumer Financing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 89\n                                            }, this),\n                                            \" for medical procedures, dental work, cosmetic surgery, and wellness treatments. We also offer Auto Consumer Financing, recreational vehicle financing, and retail purchases. All services are market-leading, providing tailored financing solutions to healthcare providers and their patients. Choose One Stop Lending Canada for a financing experience that is committed to your health and future.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Features.tsx\n");

/***/ })

};
;
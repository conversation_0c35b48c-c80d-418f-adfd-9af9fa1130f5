"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_Features_tsx",{

/***/ "(app-pages-browser)/./src/components/Features.tsx":
/*!*************************************!*\
  !*** ./src/components/Features.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst features = [\n    {\n        icon: '💳',\n        title: 'Flexible Payment Plans',\n        description: 'Customizable payment schedules that fit your budget, from 6 to 60 months.'\n    },\n    {\n        icon: '⚡',\n        title: 'Instant Approval',\n        description: 'Get approval decisions in under 24 hours with our streamlined application process.'\n    },\n    {\n        icon: '🔒',\n        title: 'Secure & Transparent',\n        description: 'Bank-level security with transparent pricing - no hidden fees or surprises.'\n    },\n    {\n        icon: '📱',\n        title: 'Easy Application',\n        description: 'Simple online application process that takes just minutes to complete.'\n    },\n    {\n        icon: '💰',\n        title: 'Competitive Rates',\n        description: 'Fair and competitive rates that make financing accessible for everyone.'\n    },\n    {\n        icon: '📊',\n        title: 'Wide Range',\n        description: 'Finance anything from $100 to $50,000 - medical procedures, purchases, and more.'\n    }\n];\nfunction Features() {\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 50\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    ref: ref,\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Why Choose \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-onestop-blue\",\n                                    children: \"One Stop Lending\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 24\n                                }, this),\n                                \"?\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-onestop-gray-dark max-w-3xl mx-auto\",\n                            children: \"We provide comprehensive financing solutions for all your needs with transparent pricing and instant approvals.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: isInView ? \"visible\" : \"hidden\",\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: itemVariants,\n                            whileHover: {\n                                scale: 1.05,\n                                transition: {\n                                    duration: 0.2\n                                }\n                            },\n                            className: \"bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.1,\n                                        rotate: 5\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    className: \"text-4xl mb-4\",\n                                    children: feature.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-onestop-gray-dark leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"text-center mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-onestop-blue to-onestop-blue-light p-8 rounded-2xl text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl md:text-3xl font-bold mb-4\",\n                                children: \"Ready to Get Financing?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl mb-6 opacity-90\",\n                                children: \"Join thousands of satisfied customers who trust One Stop Lending for their financing needs.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                href: \"#contact\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"inline-block bg-white text-onestop-blue px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-200\",\n                                children: \"Get Financing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Features.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(Features, \"DljcBprJKYjULUac3YKdUV9OwZQ=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView\n    ];\n});\n_c = Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Features.tsx\n"));

/***/ })

});
@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --onestop-blue: #1e40af;
  --onestop-blue-light: #3b82f6;
  --onestop-blue-dark: #1e3a8a;
  --onestop-gray: #f8fafc;
  --onestop-gray-dark: #64748b;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-onestop-blue: var(--onestop-blue);
  --color-onestop-blue-light: var(--onestop-blue-light);
  --color-onestop-blue-dark: var(--onestop-blue-dark);
  --color-onestop-gray: var(--onestop-gray);
  --color-onestop-gray-dark: var(--onestop-gray-dark);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

/* Prevent horizontal overflow on mobile */
* {
  box-sizing: border-box;
}

.container {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

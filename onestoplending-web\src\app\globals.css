@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
  --onestop-blue: #194178;
  --onestop-blue-light: #6a819d;
  --onestop-blue-dark: #0f2a5a;
  --onestop-gray: #f8fafc;
  --onestop-gray-dark: #64748b;
  --onestop-navy: #1f2937;
  --onestop-navy-dark: #111827;
  --onestop-secondary: #6a819d;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-onestop-blue: var(--onestop-blue);
  --color-onestop-blue-light: var(--onestop-blue-light);
  --color-onestop-blue-dark: var(--onestop-blue-dark);
  --color-onestop-gray: var(--onestop-gray);
  --color-onestop-gray-dark: var(--onestop-gray-dark);
  --color-onestop-navy: var(--onestop-navy);
  --color-onestop-navy-dark: var(--onestop-navy-dark);
  --color-onestop-secondary: var(--onestop-secondary);
  --font-sans: 'Montserrat', system-ui, -apple-system, sans-serif;
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Professional styling */
body {
  font-family: 'Montserrat', system-ui, -apple-system, sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', system-ui, -apple-system, sans-serif;
  font-weight: 600;
  line-height: 1.3;
}

.font-montserrat {
  font-family: 'Montserrat', system-ui, -apple-system, sans-serif;
}

/* Professional button styles */
.btn-primary {
  background-color: var(--onestop-blue);
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--onestop-blue-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 65, 120, 0.3);
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

/* Prevent horizontal overflow on mobile */
* {
  box-sizing: border-box;
}

.container {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

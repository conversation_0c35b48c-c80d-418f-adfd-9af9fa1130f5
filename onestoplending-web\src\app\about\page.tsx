'use client';

import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function About() {
  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-12 sm:pb-16 bg-gradient-to-br from-white via-onestop-gray to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 font-montserrat">
              About <span className="text-onestop-blue">One Stop Lending Canada</span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-onestop-gray-dark max-w-3xl mx-auto px-2 font-montserrat">
              Your trusted partner for professional healthcare financing solutions across Canada.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 font-montserrat">Our Healthcare Financing Solutions</h2>
              <p className="text-sm sm:text-base md:text-lg text-onestop-gray-dark mb-4 sm:mb-6 font-montserrat">
                One Stop Lending Canada specializes in professional healthcare financing solutions. We provide fast, reliable financing for medical procedures, dental work, cosmetic surgery, and wellness treatments. Our transparent pricing model ensures you know exactly what you'll pay upfront, with no hidden fees or surprises.
              </p>
              <p className="text-sm sm:text-base md:text-lg text-onestop-gray-dark font-montserrat">
                Whether it's essential medical procedures, dental treatments, cosmetic surgery, or other healthcare services, we make quality healthcare accessible and affordable. We also offer financing for auto purchases and other consumer needs. Our customers get the flexibility they need with competitive rates and flexible payment terms.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-onestop-blue text-white p-6 sm:p-8 rounded-2xl"
            >
              <h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 font-montserrat">How Healthcare Financing Works</h3>
              <ul className="space-y-3 sm:space-y-4">
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">🏥</span>
                  <span className="text-sm sm:text-base font-montserrat">Choose your healthcare procedure or treatment</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">⚡</span>
                  <span className="text-sm sm:text-base font-montserrat">Get instant approval in minutes, not days</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">💰</span>
                  <span className="text-sm sm:text-base font-montserrat">Transparent pricing - know exactly what you'll pay upfront</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">📅</span>
                  <span className="text-sm sm:text-base font-montserrat">Flexible payment plans that work with your budget</span>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Business Verification Section */}
      <section className="py-12 sm:py-16 bg-onestop-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 sm:mb-12"
          >
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat">Why Choose One Stop Lending Canada</h2>
            <p className="text-base sm:text-lg text-onestop-gray-dark px-2 font-montserrat">
              Trusted healthcare financing solutions with transparent pricing and instant approvals across Canada.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg"
            >
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat">Company Details</h3>
              <ul className="space-y-2 sm:space-y-3 text-onestop-gray-dark text-sm sm:text-base font-montserrat">
                <li><strong>Business Name:</strong> One Stop Lending Canada</li>
                <li><strong>Website:</strong> https://onestoplendingcanada.ca</li>
                <li><strong>Target Market:</strong> Healthcare providers and patients across Canada</li>
                <li><strong>Business Type:</strong> Healthcare & Consumer Financing Solutions</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg"
            >
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat">Our Advantages</h3>
              <ul className="space-y-2 sm:space-y-3 text-onestop-gray-dark text-sm sm:text-base font-montserrat">
                <li>✅ Instant approval process (minutes, not hours)</li>
                <li>✅ Transparent pricing with no hidden fees</li>
                <li>✅ Specialized healthcare financing solutions</li>
                <li>✅ Flexible payment terms that work for you</li>
                <li>✅ Secure and confidential application process</li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Partnership Section */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 sm:mb-12"
          >
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat">Ready to Get Healthcare Financing?</h2>
            <p className="text-sm sm:text-base md:text-lg text-onestop-gray-dark max-w-3xl mx-auto px-2 font-montserrat">
              Whether you need financing for medical procedures, dental work, cosmetic surgery, or other healthcare services, One Stop Lending Canada makes it simple and transparent. Get the funds you need with competitive rates and flexible payment terms.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg text-center"
          >
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat">Start Your Healthcare Financing Application Today</h3>
            <p className="text-sm sm:text-base text-onestop-gray-dark mb-4 sm:mb-6 px-2 font-montserrat">
              Join thousands of satisfied customers who have used One Stop Lending Canada to finance their healthcare needs. Quick approval, transparent pricing, and flexible terms.
            </p>
            <motion.a
              href="/#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-block bg-onestop-blue hover:bg-onestop-blue-dark text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-base transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Get Financing
            </motion.a>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

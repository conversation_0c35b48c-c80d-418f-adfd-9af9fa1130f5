'use client';

import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function About() {
  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-12 sm:pb-16 bg-gradient-to-br from-white via-onestop-gray to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 font-montserrat">
              About <span className="text-onestop-blue">One Stop Lending Canada</span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-onestop-gray-dark max-w-3xl mx-auto px-2 font-montserrat">
              Your trusted partner for professional healthcare financing solutions across Canada.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 font-montserrat">Our Healthcare Financing Solutions</h2>
              <p className="text-sm sm:text-base md:text-lg text-onestop-gray-dark mb-4 sm:mb-6 font-montserrat">
                One Stop Lending Canada specializes in professional healthcare financing solutions. We provide fast, reliable financing for medical procedures, dental work, cosmetic surgery, and wellness treatments. Our transparent pricing model ensures you know exactly what you'll pay upfront, with no hidden fees or surprises.
              </p>
              <p className="text-sm sm:text-base md:text-lg text-onestop-gray-dark font-montserrat">
                Whether it's essential medical procedures, dental treatments, cosmetic surgery, or other healthcare services, we make quality healthcare accessible and affordable. We also offer financing for auto purchases and other consumer needs. Our customers get the flexibility they need with competitive rates and flexible payment terms.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-onestop-blue text-white p-6 sm:p-8 rounded-2xl"
            >
              <h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 font-montserrat">How Healthcare Financing Works</h3>
              <ul className="space-y-3 sm:space-y-4">
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">🏥</span>
                  <span className="text-sm sm:text-base font-montserrat">Choose your healthcare procedure or treatment</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">⚡</span>
                  <span className="text-sm sm:text-base font-montserrat">Get instant approval in minutes, not days</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">💰</span>
                  <span className="text-sm sm:text-base font-montserrat">Transparent pricing - know exactly what you'll pay upfront</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">📅</span>
                  <span className="text-sm sm:text-base font-montserrat">Flexible payment plans that work with your budget</span>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Business Verification Section */}
      <section className="py-12 sm:py-16 bg-onestop-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 sm:mb-12"
          >
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat">Why Choose One Stop Lending Canada</h2>
            <p className="text-base sm:text-lg text-onestop-gray-dark px-2 font-montserrat">
              Trusted healthcare financing solutions with transparent pricing and instant approvals across Canada.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg"
            >
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat">Company Details</h3>
              <ul className="space-y-2 sm:space-y-3 text-onestop-gray-dark text-sm sm:text-base font-montserrat">
                <li><strong>Business Name:</strong> One Stop Lending Canada</li>
                <li><strong>Website:</strong> https://onestoplendingcanada.ca</li>
                <li><strong>Target Market:</strong> Healthcare providers and patients across Canada</li>
                <li><strong>Business Type:</strong> Healthcare & Consumer Financing Solutions</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg"
            >
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat">Our Advantages</h3>
              <ul className="space-y-2 sm:space-y-3 text-onestop-gray-dark text-sm sm:text-base font-montserrat">
                <li>✅ Instant approval process (minutes, not hours)</li>
                <li>✅ Transparent pricing with no hidden fees</li>
                <li>✅ Specialized healthcare financing solutions</li>
                <li>✅ Flexible payment terms that work for you</li>
                <li>✅ Secure and confidential application process</li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-12 sm:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-6 font-montserrat">Our Story</h2>
              <p className="text-lg text-gray-700 mb-6 font-montserrat leading-relaxed">
                Founded with a mission to make healthcare accessible to all Canadians, One Stop Lending Canada has been
                at the forefront of healthcare financing for over 11 years. We understand that quality healthcare shouldn't
                be delayed due to financial constraints.
              </p>
              <p className="text-lg text-gray-700 mb-6 font-montserrat leading-relaxed">
                Our team of financing experts works closely with healthcare providers across Canada to offer seamless,
                patient-friendly financing solutions. From routine dental work to life-changing medical procedures,
                we've helped thousands of Canadians access the care they need.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-onestop-blue mb-2 font-montserrat">6K+</div>
                  <div className="text-gray-600 font-montserrat">Healthcare Partners</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-onestop-blue mb-2 font-montserrat">$10B+</div>
                  <div className="text-gray-600 font-montserrat">Financed</div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <img
                src="https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Healthcare professionals discussing patient care"
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-onestop-blue/20 to-transparent rounded-lg"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="py-12 sm:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4 font-montserrat">Our Values</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto font-montserrat">
              These core values guide everything we do at One Stop Lending Canada
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-onestop-blue/10 rounded-full mb-6">
                <span className="text-3xl text-onestop-blue">🤝</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 font-montserrat">Trust & Transparency</h3>
              <p className="text-gray-600 font-montserrat leading-relaxed">
                We believe in complete transparency with our pricing and terms. No hidden fees, no surprises -
                just honest, straightforward financing solutions.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-onestop-blue/10 rounded-full mb-6">
                <span className="text-3xl text-onestop-blue">❤️</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 font-montserrat">Patient-Centered Care</h3>
              <p className="text-gray-600 font-montserrat leading-relaxed">
                Every decision we make is centered around improving patient access to healthcare.
                We're committed to making quality care affordable and accessible.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-onestop-blue/10 rounded-full mb-6">
                <span className="text-3xl text-onestop-blue">⚡</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 font-montserrat">Innovation & Speed</h3>
              <p className="text-gray-600 font-montserrat leading-relaxed">
                We leverage cutting-edge technology to provide instant approvals and seamless experiences,
                because healthcare can't wait.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-12 sm:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4 font-montserrat">Our Commitment to Excellence</h2>
            <p className="text-lg text-gray-600 max-w-4xl mx-auto font-montserrat leading-relaxed">
              Our experienced team of financing specialists understands the unique needs of healthcare providers and patients.
              We work tirelessly to ensure that financial barriers never prevent someone from receiving the care they need.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-onestop-blue text-white p-8 rounded-lg"
            >
              <h3 className="text-2xl font-bold mb-4 font-montserrat">For Healthcare Providers</h3>
              <ul className="space-y-3 font-montserrat">
                <li className="flex items-start">
                  <span className="text-xl mr-3">✓</span>
                  <span>Increase patient acceptance rates with flexible financing options</span>
                </li>
                <li className="flex items-start">
                  <span className="text-xl mr-3">✓</span>
                  <span>Streamlined application process that doesn't disrupt patient flow</span>
                </li>
                <li className="flex items-start">
                  <span className="text-xl mr-3">✓</span>
                  <span>Dedicated support team with healthcare industry expertise</span>
                </li>
                <li className="flex items-start">
                  <span className="text-xl mr-3">✓</span>
                  <span>No risk to your practice - we handle all credit decisions</span>
                </li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-gray-50 p-8 rounded-lg border border-gray-200"
            >
              <h3 className="text-2xl font-bold text-gray-900 mb-4 font-montserrat">For Patients</h3>
              <ul className="space-y-3 text-gray-700 font-montserrat">
                <li className="flex items-start">
                  <span className="text-xl mr-3 text-onestop-blue">✓</span>
                  <span>Quick approval decisions - often within minutes</span>
                </li>
                <li className="flex items-start">
                  <span className="text-xl mr-3 text-onestop-blue">✓</span>
                  <span>Flexible payment plans that fit your budget</span>
                </li>
                <li className="flex items-start">
                  <span className="text-xl mr-3 text-onestop-blue">✓</span>
                  <span>100% online application process</span>
                </li>
                <li className="flex items-start">
                  <span className="text-xl mr-3 text-onestop-blue">✓</span>
                  <span>Transparent pricing with no hidden fees</span>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Partnership Section */}
      <section className="py-12 sm:py-16 bg-onestop-navy text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 sm:mb-12"
          >
            <h2 className="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4 font-montserrat">Ready to Get Healthcare Financing?</h2>
            <p className="text-sm sm:text-base md:text-lg text-white/90 max-w-3xl mx-auto px-2 font-montserrat">
              Whether you need financing for medical procedures, dental work, cosmetic surgery, or other healthcare services, One Stop Lending Canada makes it simple and transparent. Get the funds you need with competitive rates and flexible payment terms.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white/10 backdrop-blur-sm p-6 sm:p-8 rounded-2xl border border-white/20 text-center"
          >
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4 font-montserrat">Start Your Healthcare Financing Application Today</h3>
            <p className="text-sm sm:text-base text-white/90 mb-4 sm:mb-6 px-2 font-montserrat">
              Join thousands of satisfied customers who have used One Stop Lending Canada to finance their healthcare needs. Quick approval, transparent pricing, and flexible terms.
            </p>
            <motion.a
              href="/#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-onestop-blue hover:bg-gray-100 px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-base transition-all duration-200 shadow-lg hover:shadow-xl font-montserrat"
            >
              Get Healthcare Financing
            </motion.a>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

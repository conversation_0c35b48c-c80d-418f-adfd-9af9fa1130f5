'use client';

import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function About() {
  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-12 sm:pb-16 bg-gradient-to-br from-white via-onestop-gray to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-4 sm:mb-6">
              About <span className="text-onestop-blue">One Stop Lending</span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-onestop-gray-dark max-w-3xl mx-auto px-2">
              Your trusted partner for instant financing solutions - from medical procedures to personal purchases.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6">Our Business Model</h2>
              <p className="text-sm sm:text-base md:text-lg text-onestop-gray-dark mb-4 sm:mb-6">
                One Stop Lending provides instant financing solutions for anything you need. We work on a simple percentage-based model - when you finance a purchase, we add a transparent fee to the total amount. For example, a $400 item becomes $500 when financed, with the $100 difference being our service fee.
              </p>
              <p className="text-sm sm:text-base md:text-lg text-onestop-gray-dark">
                Whether it's medical procedures, home improvements, electronics, or any other purchase, we make financing accessible and straightforward. Our customers get the flexibility they need, and we earn through transparent, upfront pricing.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-onestop-blue text-white p-6 sm:p-8 rounded-2xl"
            >
              <h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">How It Works</h3>
              <ul className="space-y-3 sm:space-y-4">
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">🛒</span>
                  <span className="text-sm sm:text-base">Choose what you want to finance - anything from $100 to $50,000</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">⚡</span>
                  <span className="text-sm sm:text-base">Get instant approval in under 24 hours</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">💰</span>
                  <span className="text-sm sm:text-base">Transparent pricing - know exactly what you'll pay upfront</span>
                </li>
                <li className="flex items-center">
                  <span className="text-xl sm:text-2xl mr-2 sm:mr-3">📅</span>
                  <span className="text-sm sm:text-base">Flexible payment plans that work with your budget</span>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Business Verification Section */}
      <section className="py-12 sm:py-16 bg-onestop-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 sm:mb-12"
          >
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">Why Choose One Stop Lending</h2>
            <p className="text-base sm:text-lg text-onestop-gray-dark px-2">
              Trusted financing solutions with transparent pricing and instant approvals.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg"
            >
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">Company Details</h3>
              <ul className="space-y-2 sm:space-y-3 text-onestop-gray-dark text-sm sm:text-base">
                <li><strong>Business Name:</strong> One Stop Lending</li>
                <li><strong>Website:</strong> https://onestoplendingcanada.ca</li>
                <li><strong>Target Market:</strong> Anyone needing financing in Canada</li>
                <li><strong>Business Type:</strong> Personal Financing Solutions</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg"
            >
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">Our Advantages</h3>
              <ul className="space-y-2 sm:space-y-3 text-onestop-gray-dark text-sm sm:text-base">
                <li>✅ Instant approval process (under 24 hours)</li>
                <li>✅ Transparent pricing with no hidden fees</li>
                <li>✅ Finance anything from $100 to $50,000</li>
                <li>✅ Flexible payment terms that work for you</li>
                <li>✅ Secure and confidential application process</li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Partnership Section */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-8 sm:mb-12"
          >
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">Ready to Get Financing?</h2>
            <p className="text-sm sm:text-base md:text-lg text-onestop-gray-dark max-w-3xl mx-auto px-2">
              Whether you need financing for medical procedures, home improvements, electronics, or any other purchase, One Stop Lending makes it simple and transparent. Get the funds you need with competitive rates and flexible payment terms.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg text-center"
          >
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">Start Your Application Today</h3>
            <p className="text-sm sm:text-base text-onestop-gray-dark mb-4 sm:mb-6 px-2">
              Join thousands of satisfied customers who have used One Stop Lending to finance their needs. Quick approval, transparent pricing, and flexible terms.
            </p>
            <motion.a
              href="/#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-block bg-onestop-blue hover:bg-onestop-blue-dark text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-base transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Get Financing
            </motion.a>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

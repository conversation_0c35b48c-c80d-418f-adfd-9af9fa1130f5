export default function StructuredData() {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "FinancialService",
    "name": "One Stop Lending Canada",
    "description": "Professional healthcare financing solutions in Canada. Fast approvals for medical procedures, dental work, cosmetic surgery, and more.",
    "url": "https://onestoplendingcanada.ca",
    "logo": "https://onestoplendingcanada.ca/logo.png",
    "image": "https://onestoplendingcanada.ca/logo.png",
    "telephone": "******-664-8035",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "CA",
      "addressRegion": "Canada"
    },
    "areaServed": {
      "@type": "Country",
      "name": "Canada"
    },
    "serviceType": [
      "Healthcare Financing",
      "Medical Financing",
      "Dental Financing",
      "Cosmetic Surgery Financing",
      "Auto Financing",
      "Consumer Financing"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Financing Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Healthcare Financing",
            "description": "Fast approval financing for medical procedures and healthcare services"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Dental Financing",
            "description": "Flexible payment plans for dental treatments and procedures"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Auto Financing",
            "description": "Vehicle financing solutions with competitive rates"
          }
        }
      ]
    },
    "sameAs": [
      "https://www.facebook.com/onestoplendingcanada",
      "https://www.instagram.com/onestoplendingcanada"
    ]
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "One Stop Lending Canada",
    "url": "https://onestoplendingcanada.ca",
    "description": "Professional healthcare financing solutions in Canada",
    "publisher": {
      "@type": "Organization",
      "name": "One Stop Lending Canada"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://onestoplendingcanada.ca/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://onestoplendingcanada.ca"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Healthcare Financing",
        "item": "https://onestoplendingcanada.ca/#healthcare"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": "Contact",
        "item": "https://onestoplendingcanada.ca/#contact"
      }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
    </>
  );
}

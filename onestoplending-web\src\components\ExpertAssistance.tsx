'use client';

import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

export default function ExpertAssistance() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section className="py-20 bg-onestop-navy text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <motion.div
            ref={ref}
            initial={{ opacity: 0, x: -30 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }}
            transition={{ duration: 0.8 }}
          >
            <div className="text-sm text-white/70 mb-2 font-montserrat">From start to finish</div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-montserrat">
              Expert Assistance at Every Step
            </h2>
            <p className="text-lg text-white/90 leading-relaxed font-montserrat">
              Our dedicated team is here to assist you. We deal with experienced people who know the 
              industry thoroughly. We streamline the process from the beginning right through to 
              contact, followed by approval. We are there for any time you want (in whatever manner is 
              convenient) and will respond ASAP.
            </p>
          </motion.div>

          {/* Right Content - Professional Image */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="relative rounded-lg overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Professional customer service team"
                className="w-full h-96 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-onestop-navy/80 to-transparent"></div>
              <div className="absolute bottom-0 left-0 right-0 p-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl mb-1">📞</div>
                    <h4 className="font-semibold text-sm font-montserrat">24/7 Support</h4>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl mb-1">👥</div>
                    <h4 className="font-semibold text-sm font-montserrat">Expert Team</h4>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

'use client';

import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

const features = [
  {
    icon: '⚡',
    title: 'Fast Approvals',
    description: 'Get approved in minutes with our streamlined application process and instant credit decisions.',
  },
  {
    icon: '🤝',
    title: 'Committed Service',
    description: 'Our dedicated team provides exceptional service and support throughout your financing journey.',
  },
  {
    icon: '💻',
    title: '100% Online Process',
    description: 'Complete applications, upload documents, and sign agreements electronically from any device.',
  },
  {
    icon: '📈',
    title: 'Increase Your Sales',
    description: 'Our point-of-sale financing solutions help your customers make purchases while paying over time.',
  },
  {
    icon: '🔒',
    title: 'Secure & Reliable',
    description: 'Bank-level security with transparent pricing and no hidden fees or surprises.',
  },
  {
    icon: '📞',
    title: 'Expert Support',
    description: 'Speak to real people with industry knowledge who respond quickly to your needs.',
  },
];

export default function Features() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 font-montserrat">
            Fast Approvals,
          </h2>
          <h3 className="text-3xl md:text-4xl font-bold text-onestop-blue mb-6 font-montserrat">
            Committed Service
          </h3>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto font-montserrat leading-relaxed">
            We understand that you have options when it comes to financing solutions at One Stop Lending Canada.
            Our fast approvals and our exceptional service are what set us apart. With our online financing portal,
            the entire financing process is streamlined offering real-time customer verification, instant credit decisions, and quick funding.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200"
            >
              <motion.div
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.2 }}
                className="text-4xl mb-6 text-onestop-blue"
              >
                {feature.icon}
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 font-montserrat">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed font-montserrat">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Professional Services Description */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-white p-8 rounded-lg shadow-md border border-gray-200">
            <p className="text-lg text-gray-700 font-montserrat leading-relaxed max-w-4xl mx-auto">
              Friendly Easy Consumer Financing Solutions To Partner With Your Business. Auto Consumer Financing,
              Healthcare Consumer Financing, Motorcycles, Jet Skis, ATVs, RVs, Retail Purchases. All of these services are
              market-leading, providing tailored service offerings to the various sectors that our clients and their customers
              come from. Choose One Stop Lending Canada for a financing experience that is committed to your future.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

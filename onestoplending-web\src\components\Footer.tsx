'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

export default function Footer() {
  const quickLinks = [
    { name: 'Auto Consumer Financing', href: '#' },
    { name: 'Healthcare Consumer Financing', href: '#' },
    { name: 'About Us', href: '/about' },
    { name: 'Blog', href: '#' },
  ];

  return (
    <footer className="bg-onestop-navy-dark text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Logo and Company Info */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Image
                src="/logo.png"
                alt="One Stop Lending Logo"
                width={300}
                height={120}
                className="h-24 w-auto mb-6 filter brightness-0 invert"
              />
            </motion.div>
          </div>

          {/* Quick Links */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h3 className="text-lg font-semibold mb-6 font-montserrat">Quick Links</h3>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-200 font-montserrat"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Contact Info */}
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h3 className="text-lg font-semibold mb-6 font-montserrat">Contact</h3>
              <div className="space-y-3 text-gray-300">
                <p className="font-montserrat">
                  <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </p>
                <p className="font-montserrat">
                  <a href="tel:+14034022015" className="hover:text-white transition-colors">
                    +****************
                  </a>
                </p>
                <div className="flex space-x-4 mt-6">
                  <motion.a
                    href="#"
                    whileHover={{ scale: 1.1 }}
                    className="text-2xl hover:text-onestop-blue transition-colors duration-200"
                    aria-label="Instagram"
                  >
                    📷
                  </motion.a>
                  <motion.a
                    href="#"
                    whileHover={{ scale: 1.1 }}
                    className="text-2xl hover:text-onestop-blue transition-colors duration-200"
                    aria-label="Facebook"
                  >
                    📘
                  </motion.a>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="border-t border-gray-700 mt-12 pt-8"
        >
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm font-montserrat mb-4 md:mb-0">
              ©2025 All rights Reserved | Powered by <a href="#" className="text-onestop-blue hover:text-onestop-blue-light">SEO Support</a>
            </p>
            <div className="flex space-x-6">
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors duration-200 font-montserrat">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors duration-200 font-montserrat">
                Terms & Conditions
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_BusinessSection_tsx"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inView: () => (/* binding */ inView)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/index.mjs\");\n\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (typeof onEnd === \"function\") {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-in-view.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInView: () => (/* binding */ useInView)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../render/dom/viewport/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\");\n\n\n\nfunction useInView(ref, { root, margin, amount, once = false } = {}) {\n    const [isInView, setInView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return (0,_render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__.inView)(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWluLXZpZXcubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNjOztBQUUxRCwwQkFBMEIscUNBQXFDLElBQUk7QUFDbkUsa0NBQWtDLCtDQUFRO0FBQzFDLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzRUFBTTtBQUNyQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZ3Rtb3RcXERvY3VtZW50c1xcR2l0SHViXFxPbmVTdG9wTGVuZGluZ1dlYnNpdGVcXG9uZXN0b3BsZW5kaW5nLXdlYlxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdXRpbHNcXHVzZS1pbi12aWV3Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaW5WaWV3IH0gZnJvbSAnLi4vcmVuZGVyL2RvbS92aWV3cG9ydC9pbmRleC5tanMnO1xuXG5mdW5jdGlvbiB1c2VJblZpZXcocmVmLCB7IHJvb3QsIG1hcmdpbiwgYW1vdW50LCBvbmNlID0gZmFsc2UgfSA9IHt9KSB7XG4gICAgY29uc3QgW2lzSW5WaWV3LCBzZXRJblZpZXddID0gdXNlU3RhdGUoZmFsc2UpO1xuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmICghcmVmLmN1cnJlbnQgfHwgKG9uY2UgJiYgaXNJblZpZXcpKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25zdCBvbkVudGVyID0gKCkgPT4ge1xuICAgICAgICAgICAgc2V0SW5WaWV3KHRydWUpO1xuICAgICAgICAgICAgcmV0dXJuIG9uY2UgPyB1bmRlZmluZWQgOiAoKSA9PiBzZXRJblZpZXcoZmFsc2UpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCBvcHRpb25zID0ge1xuICAgICAgICAgICAgcm9vdDogKHJvb3QgJiYgcm9vdC5jdXJyZW50KSB8fCB1bmRlZmluZWQsXG4gICAgICAgICAgICBtYXJnaW4sXG4gICAgICAgICAgICBhbW91bnQsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiBpblZpZXcocmVmLmN1cnJlbnQsIG9uRW50ZXIsIG9wdGlvbnMpO1xuICAgIH0sIFtyb290LCByZWYsIG1hcmdpbiwgb25jZSwgYW1vdW50XSk7XG4gICAgcmV0dXJuIGlzSW5WaWV3O1xufVxuXG5leHBvcnQgeyB1c2VJblZpZXcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/BusinessSection.tsx":
/*!********************************************!*\
  !*** ./src/components/BusinessSection.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BusinessSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst businessFeatures = [\n    {\n        icon: '🚀',\n        title: 'Increase Profits',\n        description: 'Our point-of-sale financing solutions allow your customers to make big-ticket purchases today while paying over time. This increases your sales volume and overall profit.'\n    },\n    {\n        icon: '⚡',\n        title: 'Lightning-Fast Approvals',\n        description: 'Get customer approvals within seconds with our quick application process and get your funds direct deposited within a few days of the deal being booked.'\n    },\n    {\n        icon: '💻',\n        title: '100% Online Process',\n        description: 'Complete applications in minutes and get instant credit decisions right at the point of sale. Use any device to submit applications and sign agreements electronically.'\n    },\n    {\n        icon: '🤝',\n        title: 'We\\'re Here for You',\n        description: 'We\\'re here to support you. You\\'ll speak to real people with a wealth of industry knowledge. Contact us in the way that works best for you.'\n    }\n];\nconst stats = [\n    {\n        number: '6K+',\n        label: 'Partners'\n    },\n    {\n        number: '11+',\n        label: 'Years as Canada\\'s leading powersports lender'\n    },\n    {\n        number: '$10B+',\n        label: 'In loan applications processed'\n    }\n];\nfunction BusinessSection() {\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 50\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"businesses\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    ref: ref,\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Point-of-Sale Financing\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gradient\",\n                                    children: \"Designed for Businesses\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-onestop-gray-dark max-w-4xl mx-auto leading-relaxed\",\n                            children: \"In point-of-sale financing, we know you have choices. But with our all-in-one financing center offering instant online credit decisions, real-time customer verification, and industry-leading dealer reserves, we believe we're the obvious choice.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: isInView ? \"visible\" : \"hidden\",\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-20\",\n                    children: businessFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            variants: itemVariants,\n                            className: \"bg-gray-50 p-8 rounded-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: feature.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-onestop-gray-dark leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-12\",\n                            children: \"The Numbers Speak for Themselves\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: isInView ? {\n                                        opacity: 1,\n                                        scale: 1\n                                    } : {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.6 + index * 0.1\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl md:text-5xl font-bold text-onestop-blue mb-2\",\n                                            children: stat.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-onestop-gray-dark text-lg\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.8\n                    },\n                    className: \"text-center bg-gray-50 p-12 rounded-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-6\",\n                            children: \"Partner with us and start submitting loan applications within days\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"#contact\",\n                            className: \"btn-professional text-white px-8 py-4 rounded-lg text-lg font-semibold inline-block\",\n                            children: \"Join Our Network\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\BusinessSection.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessSection, \"DljcBprJKYjULUac3YKdUV9OwZQ=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView\n    ];\n});\n_c = BusinessSection;\nvar _c;\n$RefreshReg$(_c, \"BusinessSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/BusinessSection.tsx\n"));

/***/ })

}]);
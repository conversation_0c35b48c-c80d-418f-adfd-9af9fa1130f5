'use client';

import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

const services = [
  {
    icon: '💳',
    title: '100% Free Application with no hidden charges',
    description: 'Join us without any fees or the need for a credit card.',
  },
  {
    icon: '🚗',
    title: 'Effortless auto loan applications',
    description: 'Apply for auto loans with minimal paperwork and hassle.',
  },
  {
    icon: '🏥',
    title: 'Comprehensive healthcare financing solutions',
    description: 'Discover a variety of healthcare financing options tailored to your needs.',
  },
  {
    icon: '📱',
    title: 'Electronic submissions and signatures',
    description: 'Easily upload documents and sign electronically from any device.',
  },
  {
    icon: '💻',
    title: 'Borrowing portal for loan management',
    description: 'Access your loans and manage them through our convenient borrowing portal.',
  },
  {
    icon: '🎯',
    title: 'Focus on what matters while we manage the financing',
    description: 'We will handle all the financing details, so you won\'t need to worry about it.',
  },
];

export default function StreamlineFinancing() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-onestop-blue mb-6 font-montserrat">
            Streamline your Financing
          </h2>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="text-center"
            >
              <motion.div
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.2 }}
                className="inline-flex items-center justify-center w-16 h-16 bg-onestop-blue/10 rounded-full mb-6"
              >
                <span className="text-2xl text-onestop-blue">{service.icon}</span>
              </motion.div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 font-montserrat">
                {service.title}
              </h3>
              <p className="text-gray-600 leading-relaxed font-montserrat">
                {service.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Statistics Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-20 text-center"
        >
          <div className="bg-gray-50 rounded-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <div className="text-4xl font-bold text-onestop-blue mb-2 font-montserrat">6K+</div>
                <div className="text-gray-600 font-montserrat">Partners</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-onestop-blue mb-2 font-montserrat">11+</div>
                <div className="text-gray-600 font-montserrat">Years as Canada's leading lender</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-onestop-blue mb-2 font-montserrat">$10B</div>
                <div className="text-gray-600 font-montserrat">In loan applications processed</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

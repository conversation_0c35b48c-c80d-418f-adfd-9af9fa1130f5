"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/app/about/page.tsx":
/*!********************************!*\
  !*** ./src/app/about/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction About() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-20 sm:pt-24 pb-12 sm:pb-16 bg-gradient-to-br from-white via-onestop-gray to-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 font-montserrat\",\n                                children: [\n                                    \"About \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-onestop-blue\",\n                                        children: \"One Stop Lending Canada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base sm:text-lg md:text-xl text-onestop-gray-dark max-w-3xl mx-auto px-2 font-montserrat\",\n                                children: \"Your trusted partner for professional healthcare financing solutions across Canada.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 font-montserrat\",\n                                        children: \"Our Healthcare Financing Solutions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg text-onestop-gray-dark mb-4 sm:mb-6 font-montserrat\",\n                                        children: \"One Stop Lending Canada specializes in professional healthcare financing solutions. We provide fast, reliable financing for medical procedures, dental work, cosmetic surgery, and wellness treatments. Our transparent pricing model ensures you know exactly what you'll pay upfront, with no hidden fees or surprises.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base md:text-lg text-onestop-gray-dark font-montserrat\",\n                                        children: \"Whether it's essential medical procedures, dental treatments, cosmetic surgery, or other healthcare services, we make quality healthcare accessible and affordable. We also offer financing for auto purchases and other consumer needs. Our customers get the flexibility they need with competitive rates and flexible payment terms.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"bg-onestop-blue text-white p-6 sm:p-8 rounded-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl sm:text-2xl font-bold mb-3 sm:mb-4 font-montserrat\",\n                                        children: \"How Healthcare Financing Works\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3 sm:space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl sm:text-2xl mr-2 sm:mr-3\",\n                                                        children: \"\\uD83C\\uDFE5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm sm:text-base font-montserrat\",\n                                                        children: \"Choose your healthcare procedure or treatment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl sm:text-2xl mr-2 sm:mr-3\",\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm sm:text-base font-montserrat\",\n                                                        children: \"Get instant approval in minutes, not days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl sm:text-2xl mr-2 sm:mr-3\",\n                                                        children: \"\\uD83D\\uDCB0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm sm:text-base font-montserrat\",\n                                                        children: \"Transparent pricing - know exactly what you'll pay upfront\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl sm:text-2xl mr-2 sm:mr-3\",\n                                                        children: \"\\uD83D\\uDCC5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm sm:text-base font-montserrat\",\n                                                        children: \"Flexible payment plans that work with your budget\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16 bg-onestop-gray\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-8 sm:mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat\",\n                                    children: \"Why Choose One Stop Lending Canada\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base sm:text-lg text-onestop-gray-dark px-2 font-montserrat\",\n                                    children: \"Trusted healthcare financing solutions with transparent pricing and instant approvals across Canada.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-6 sm:p-8 rounded-2xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat\",\n                                            children: \"Company Details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 sm:space-y-3 text-onestop-gray-dark text-sm sm:text-base font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Business Name:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" One Stop Lending Canada\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Website:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" https://onestoplendingcanada.ca\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Target Market:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Healthcare providers and patients across Canada\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Business Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Healthcare & Consumer Financing Solutions\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-white p-6 sm:p-8 rounded-2xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat\",\n                                            children: \"Our Advantages\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 sm:space-y-3 text-onestop-gray-dark text-sm sm:text-base font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Instant approval process (minutes, not hours)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Transparent pricing with no hidden fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Specialized healthcare financing solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Flexible payment terms that work for you\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"✅ Secure and confidential application process\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-6 font-montserrat\",\n                                        children: \"Our Story\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-700 mb-6 font-montserrat leading-relaxed\",\n                                        children: \"Founded with a mission to make healthcare accessible to all Canadians, One Stop Lending Canada has been at the forefront of healthcare financing for over 11 years. We understand that quality healthcare shouldn't be delayed due to financial constraints.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-700 mb-6 font-montserrat leading-relaxed\",\n                                        children: \"Our team of financing experts works closely with healthcare providers across Canada to offer seamless, patient-friendly financing solutions. From routine dental work to life-changing medical procedures, we've helped thousands of Canadians access the care they need.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-onestop-blue mb-2 font-montserrat\",\n                                                        children: \"6K+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-600 font-montserrat\",\n                                                        children: \"Healthcare Partners\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-onestop-blue mb-2 font-montserrat\",\n                                                        children: \"$10B+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-600 font-montserrat\",\n                                                        children: \"Financed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                                        alt: \"Healthcare professionals discussing patient care\",\n                                        className: \"w-full h-96 object-cover rounded-lg shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-t from-onestop-blue/20 to-transparent rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4 font-montserrat\",\n                                    children: \"Our Values\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto font-montserrat\",\n                                    children: \"These core values guide everything we do at One Stop Lending Canada\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-16 h-16 bg-onestop-blue/10 rounded-full mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-3xl text-onestop-blue\",\n                                                children: \"\\uD83E\\uDD1D\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4 font-montserrat\",\n                                            children: \"Trust & Transparency\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 font-montserrat leading-relaxed\",\n                                            children: \"We believe in complete transparency with our pricing and terms. No hidden fees, no surprises - just honest, straightforward financing solutions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-16 h-16 bg-onestop-blue/10 rounded-full mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-3xl text-onestop-blue\",\n                                                children: \"❤️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4 font-montserrat\",\n                                            children: \"Patient-Centered Care\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 font-montserrat leading-relaxed\",\n                                            children: \"Every decision we make is centered around improving patient access to healthcare. We're committed to making quality care affordable and accessible.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.3\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center w-16 h-16 bg-onestop-blue/10 rounded-full mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-3xl text-onestop-blue\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4 font-montserrat\",\n                                            children: \"Innovation & Speed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 font-montserrat leading-relaxed\",\n                                            children: \"We leverage cutting-edge technology to provide instant approvals and seamless experiences, because healthcare can't wait.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4 font-montserrat\",\n                                    children: \"Our Commitment to Excellence\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-4xl mx-auto font-montserrat leading-relaxed\",\n                                    children: \"Our experienced team of financing specialists understands the unique needs of healthcare providers and patients. We work tirelessly to ensure that financial barriers never prevent someone from receiving the care they need.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-onestop-blue text-white p-8 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold mb-4 font-montserrat\",\n                                            children: \"For Healthcare Providers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Increase patient acceptance rates with flexible financing options\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Streamlined application process that doesn't disrupt patient flow\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Dedicated support team with healthcare industry expertise\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl mr-3\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"No risk to your practice - we handle all credit decisions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"bg-gray-50 p-8 rounded-lg border border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-4 font-montserrat\",\n                                            children: \"For Patients\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-gray-700 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl mr-3 text-onestop-blue\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Quick approval decisions - often within minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl mr-3 text-onestop-blue\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Flexible payment plans that fit your budget\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl mr-3 text-onestop-blue\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"100% online application process\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl mr-3 text-onestop-blue\",\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Transparent pricing with no hidden fees\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-16 bg-onestop-navy text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-center mb-8 sm:mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-3xl font-bold mb-3 sm:mb-4 font-montserrat\",\n                                    children: \"Ready to Get Healthcare Financing?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base md:text-lg text-white/90 max-w-3xl mx-auto px-2 font-montserrat\",\n                                    children: \"Whether you need financing for medical procedures, dental work, cosmetic surgery, or other healthcare services, One Stop Lending Canada makes it simple and transparent. Get the funds you need with competitive rates and flexible payment terms.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white p-6 sm:p-8 rounded-2xl shadow-lg text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4 font-montserrat\",\n                                    children: \"Start Your Healthcare Financing Application Today\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-onestop-gray-dark mb-4 sm:mb-6 px-2 font-montserrat\",\n                                    children: \"Join thousands of satisfied customers who have used One Stop Lending Canada to finance their healthcare needs. Quick approval, transparent pricing, and flexible terms.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                    href: \"/#contact\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"inline-block bg-onestop-blue hover:bg-onestop-blue-dark text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-base transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: \"Get Financing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\app\\\\about\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = About;\nvar _c;\n$RefreshReg$(_c, \"About\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/about/page.tsx\n"));

/***/ })

});